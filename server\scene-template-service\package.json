{"name": "scene-template-service", "version": "1.0.0", "description": "场景模板微服务，支持预定义场景模板系统、模板参数化和定制、模板分享和评价、模板版本管理", "main": "dist/main.js", "scripts": {"build": "tsc", "start": "node dist/main.js", "start:dev": "ts-node --transpile-only src/main.ts", "start:debug": "ts-node --inspect src/main.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/typeorm": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/swagger": "^7.0.0", "@nestjs/jwt": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/microservices": "^10.0.0", "@nestjs/schedule": "^4.0.0", "typeorm": "^0.3.17", "pg": "^8.11.0", "redis": "^4.6.0", "minio": "^7.1.0", "uuid": "^9.0.0", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "bcrypt": "^5.1.0", "compression": "^1.7.4", "helmet": "^7.0.0", "express-rate-limit": "^6.8.0", "winston": "^3.10.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.0", "lodash": "^4.17.21", "joi": "^17.9.0", "sharp": "^0.32.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/express": "^4.17.17", "@types/uuid": "^9.0.0", "@types/bcrypt": "^5.0.0", "@types/passport-jwt": "^3.0.8", "@types/jest": "^29.5.0", "@types/lodash": "^4.14.195", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.0", "typescript": "^5.1.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.spec.ts", "!src/**/*.interface.ts"]}, "keywords": ["scene-template", "microservice", "3d-scenes", "<PERSON><PERSON><PERSON>", "typescript", "template-management"], "author": "DL Engine Team", "license": "MIT"}